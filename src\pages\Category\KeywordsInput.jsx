import { useState } from 'react';
import { useField } from 'formik';

const KeywordsInput = ({ index, ...props }) => {
  const [field, meta, helpers] = useField(props);
  const { value } = meta;
  const { setValue } = helpers;
  const [inputValue, setInputValue] = useState('');

  const keywords = value ? value.split(',').filter(k => k.trim() !== '') : [];

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault();
      const newKeyword = inputValue.trim();
      if (newKeyword && !keywords.includes(newKeyword)) {
        const updatedKeywords = [...keywords, newKeyword].join(',');
        setValue(updatedKeywords);
        setInputValue('');
      }
    }
  };

  const removeKeyword = (keywordToRemove) => {
    const updatedKeywords = keywords.filter(k => k !== keywordToRemove).join(',');
    setValue(updatedKeywords);
  };

  return (
    <div>
      <label htmlFor={`translations.${index}.seo_keywords`} className="block mb-1 font-medium">
        SEO Keywords
      </label>
      <div className="flex flex-wrap items-center gap-2 p-2 border rounded focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500">
        {keywords.map((keyword, i) => (
          <div key={i} className="flex items-center bg-gray-100 px-2 py-1 rounded-full text-sm">
            {keyword}
            <button
              type="button"
              onClick={() => removeKeyword(keyword)}
              className="ml-1 text-gray-500 hover:text-red-500"
            >
              &times;
            </button>
          </div>
        ))}
        <input
          type="text"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyDown={handleKeyDown}
          className="flex-1 min-w-[100px] p-1 outline-none"
          placeholder="Type and press Enter"
        />
      </div>
      {meta.touched && meta.error && (
        <p className="text-red-500 text-sm mt-1">{meta.error}</p>
      )}
    </div>
  );
};

export default KeywordsInput;
