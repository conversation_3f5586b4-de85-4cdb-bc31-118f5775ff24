import React, { useState, useMemo } from "react";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import Badge from "@/components/ui/Badge";
import Card from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Icon from "@/components/ui/Icon";
import { Link, useNavigate, useParams } from "react-router-dom";
import Delete from "./Delete";
import Pagination from "@/components/partials/common-table/pagination";

const CategoryList = () => {
  const navigate = useNavigate();
  const { status } = useParams();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteData, setDeleteData] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [filters, setFilters] = useState({
    featured: '',
    premium: '',
    is_active: '',
    search: '',
    sort_by: 'title',
    sort_order: 'asc'
  });

  const queryParam = useMemo(() => {
    const params = new URLSearchParams();

    // Add status-based filters
    if (status === "premium") {
      params.append('premium', 'true');
    } else if (status === "featured") {
      params.append('featured', 'true');
    }

    // Add manual filters
    if (filters.featured) params.append('featured', filters.featured);
    if (filters.premium) params.append('premium', filters.premium);
    if (filters.is_active) params.append('is_active', filters.is_active);
    if (filters.search) params.append('search', filters.search);
    if (filters.sort_by) params.append('sort_by', filters.sort_by);
    if (filters.sort_order) params.append('sort_order', filters.sort_order);

    // Add pagination
    if (currentPage > 1) params.append('page', currentPage.toString());
    params.append('limit', '10');

    const queryString = params.toString();
    return queryString ? `?${queryString}` : '';
  }, [status, filters, currentPage]);

  const { data: categories, isLoading, isFetching } = useGetApiQuery(
    "admin/categories" + queryParam
  );

  const handleCategoryClick = (category) => {
    setSelectedCategory(category);
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
    setCurrentPage(1); // Reset to first page when filtering
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const getPageTitle = () => {
    switch (status) {
      case 'premium':
        return 'Premium Categories';
      case 'featured':
        return 'Featured Categories';
      default:
        return 'All Categories';
    }
  };

  const renderFilterBar = () => (
    <div className="flex flex-col sm:flex-row gap-4 p-4 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
      {/* Search */}
      <div className="flex-1">
        <input
          type="text"
          placeholder="Search categories..."
          value={filters.search}
          onChange={(e) => handleFilterChange('search', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
        />
      </div>

      {/* Featured Filter */}
      <select
        value={filters.featured}
        onChange={(e) => handleFilterChange('featured', e.target.value)}
        className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"
      >
        <option value="">All Categories</option>
        <option value="true">Featured Only</option>
        <option value="false">Non-Featured</option>
      </select>

      {/* Premium Filter */}
      <select
        value={filters.premium}
        onChange={(e) => handleFilterChange('premium', e.target.value)}
        className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"
      >
        <option value="">All Types</option>
        <option value="true">Premium Only</option>
        <option value="false">Regular</option>
      </select>

      {/* Active Status Filter */}
      <select
        value={filters.is_active}
        onChange={(e) => handleFilterChange('is_active', e.target.value)}
        className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"
      >
        <option value="">All Status</option>
        <option value="true">Active Only</option>
        <option value="false">Inactive Only</option>
      </select>

      {/* Sort */}
      <select
        value={`${filters.sort_by}-${filters.sort_order}`}
        onChange={(e) => {
          const [sort_by, sort_order] = e.target.value.split('-');
          handleFilterChange('sort_by', sort_by);
          handleFilterChange('sort_order', sort_order);
        }}
        className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"
      >
        <option value="title-asc">Title A-Z</option>
        <option value="title-desc">Title Z-A</option>
        <option value="created_at-desc">Newest First</option>
        <option value="created_at-asc">Oldest First</option>
      </select>
    </div>
  );

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Categories List */}
        <div className="lg:col-span-2">
          <Card>
            {/* Header */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 p-6 border-b border-gray-200 dark:border-gray-700">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {getPageTitle()}
                </h1>
                <p className="text-gray-500 dark:text-gray-400 mt-1">
                  Manage your categories and organize your content
                </p>
              </div>

              <Button
                icon="heroicons:plus"
                onClick={() => navigate('/category/create')}
                className="bg-primary-500 hover:bg-primary-600 text-white"
              >
                Create Category
              </Button>
            </div>

            {/* Filters */}
            {renderFilterBar()}

            {/* Categories List */}
            <div className="p-6">
              {isLoading || isFetching ? (
                <div className="flex justify-center items-center h-64">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
                </div>
              ) : categories?.data?.length > 0 ? (
                <div className="space-y-3">
                  {categories.data.map((category) => (
                    <div
                      key={category.id}
                      onClick={() => handleCategoryClick(category)}
                      className={`flex items-center gap-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md ${
                        selectedCategory?.id === category.id
                          ? 'bg-primary-50 dark:bg-primary-900/20 border-primary-200 dark:border-primary-700'
                          : 'bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700'
                      }`}
                    >
                      {/* Image */}
                      <div className="relative h-16 w-16 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-600 flex-shrink-0">
                        {category.image ? (
                          <img
                            src={import.meta.env.VITE_ASSET_HOST_URL + category.image}
                            alt={category.title}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                            <Icon icon="heroicons:photo" className="w-6 h-6 text-gray-400" />
                          </div>
                        )}
                      </div>

                      {/* Content */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="font-medium text-gray-900 dark:text-white truncate text-sm">
                            {category.title}
                          </h3>
                          <Badge
                            className={
                              category.is_active
                                ? 'bg-green-100 text-green-800 text-xs'
                                : 'bg-red-100 text-red-800 text-xs'
                            }
                          >
                            {category.is_active ? 'Active' : 'Inactive'}
                          </Badge>
                          {category.is_featured && (
                            <Badge className="bg-yellow-100 text-yellow-800 text-xs">
                              Featured
                            </Badge>
                          )}
                          {category.is_premium && (
                            <Badge className="bg-purple-100 text-purple-800 text-xs">
                              Premium
                            </Badge>
                          )}
                        </div>
                        <p className="text-gray-600 dark:text-gray-400 text-xs truncate">
                          {category.description || 'No description available'}
                        </p>
                      </div>

                      {/* Subcategory Count */}
                      <div className="text-center flex-shrink-0">
                        <div className="text-sm font-medium text-primary-600 dark:text-primary-400">
                          {category.sub_categories?.length || 0}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          Subcategories
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex items-center gap-2 flex-shrink-0">
                        <Link
                          to={`/category/${category.id}`}
                          className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-full transition-colors"
                          title="View Details"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <Icon icon="heroicons:eye" className="w-4 h-4" />
                        </Link>
                        <Link
                          to={`/categories/edit/${category.id}`}
                          className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 dark:hover:bg-green-900/20 rounded-full transition-colors"
                          title="Edit"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <Icon icon="heroicons:pencil-square" className="w-4 h-4" />
                        </Link>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setDeleteData(category);
                            setShowDeleteModal(true);
                          }}
                          className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-full transition-colors"
                          title="Delete"
                        >
                          <Icon icon="heroicons:trash" className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Icon icon="heroicons:folder-open" className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No categories found</h3>
                  <p className="text-gray-500 dark:text-gray-400 mb-6">Get started by creating your first category.</p>
                  <Button
                    icon="heroicons:plus"
                    onClick={() => navigate('/category/create')}
                    className="bg-primary-500 hover:bg-primary-600 text-white"
                  >
                    Create Category
                  </Button>
                </div>
              )}

              {/* Pagination */}
              {categories?.meta?.count > 0 && (
                <div className="flex justify-center mt-6">
                  <Pagination
                    totalPages={Math.ceil(categories.meta.count / 10)}
                    currentPage={currentPage}
                    handlePageChange={handlePageChange}
                  />
                </div>
              )}
            </div>
          </Card>
        </div>

        {/* Subcategory Details Panel */}
        <div className="lg:col-span-1">
          <Card>
            {selectedCategory ? (
              <div className="p-6">
                {/* Category Header */}
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-4 mb-6 border border-blue-100 dark:border-blue-800">
                  <div className="flex items-center gap-4">
                    <div className="relative">
                      {selectedCategory.image ? (
                        <img
                          src={import.meta.env.VITE_ASSET_HOST_URL + selectedCategory.image}
                          alt={selectedCategory.title}
                          className="w-14 h-14 rounded-xl object-cover border-2 border-white dark:border-gray-700 shadow-sm"
                        />
                      ) : (
                        <div className="w-14 h-14 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-sm">
                          <Icon icon="heroicons:folder" className="w-6 h-6 text-white" />
                        </div>
                      )}
                      {selectedCategory.is_featured && (
                        <div className="absolute -top-1 -right-1 w-5 h-5 bg-yellow-400 rounded-full flex items-center justify-center shadow-sm">
                          <Icon icon="heroicons:star" className="w-2.5 h-2.5 text-white" />
                        </div>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="font-semibold text-gray-900 dark:text-white text-base truncate">
                        {selectedCategory.title}
                      </h3>
                      <p className="text-xs text-gray-600 dark:text-gray-300 mt-1 line-clamp-2">
                        {selectedCategory.description || 'No description available'}
                      </p>
                      <div className="flex items-center gap-3 mt-2">
                        <div className="flex items-center gap-1 text-xs text-blue-600 dark:text-blue-400">
                          <Icon icon="heroicons:folder" className="w-3 h-3" />
                          <span>{selectedCategory.sub_categories?.length || 0} subcategories</span>
                        </div>
                        {selectedCategory.is_premium && (
                          <Badge className="bg-purple-100 text-purple-800 text-xs">
                            Premium
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Subcategories Header */}
                <div className="border-b border-gray-200 dark:border-gray-700 pb-4 mb-6">
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-white text-base">
                        Subcategories
                      </h4>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {selectedCategory.sub_categories?.length || 0} subcategories found
                      </p>
                    </div>
                    <Link
                      to={`/category/${selectedCategory.id}`}
                      className="inline-flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                    >
                      <Icon icon="heroicons:cog-6-tooth" className="w-4 h-4" />
                      Manage Category
                    </Link>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-3">
                    <button
                      onClick={() => {
                        navigate(`/category/${selectedCategory.id}#create-subcategory`);
                      }}
                      className="flex-1 inline-flex items-center justify-center gap-2 px-4 py-2.5 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
                    >
                      <Icon icon="heroicons:plus" className="w-4 h-4" />
                      Add Subcategory
                    </button>
                    <Link
                      to={`/category/${selectedCategory.id}/bulk-upload`}
                      className="flex-1 inline-flex items-center justify-center gap-2 px-4 py-2.5 text-sm font-medium text-blue-700 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 border border-blue-200 dark:border-blue-700 rounded-lg transition-all duration-200"
                    >
                      <Icon icon="heroicons:cloud-arrow-up" className="w-4 h-4" />
                      Bulk Upload
                    </Link>
                  </div>
                </div>

                {/* Subcategories List */}
                <div>
                  {selectedCategory.sub_categories?.length > 0 ? (
                    <div className="space-y-2">
                      {selectedCategory.sub_categories.map((subcategory) => (
                        <Link
                          key={subcategory.id}
                          to={`/subcategory/${subcategory.id}`}
                          className="group flex items-center gap-3 p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl hover:border-blue-300 dark:hover:border-blue-600 hover:shadow-md transition-all duration-200"
                        >
                          <div className="relative">
                            {subcategory.image ? (
                              <img
                                src={import.meta.env.VITE_ASSET_HOST_URL + subcategory.image}
                                alt={subcategory.title}
                                className="w-12 h-12 rounded-lg object-cover border border-gray-200 dark:border-gray-600"
                              />
                            ) : (
                              <div className="w-12 h-12 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg flex items-center justify-center border border-blue-200 dark:border-blue-700">
                                <Icon icon="heroicons:folder" className="w-5 h-5 text-blue-500 dark:text-blue-400" />
                              </div>
                            )}
                            {subcategory.is_featured && (
                              <div className="absolute -top-1 -right-1 w-4 h-4 bg-yellow-400 rounded-full flex items-center justify-center">
                                <Icon icon="heroicons:star" className="w-2.5 h-2.5 text-white" />
                              </div>
                            )}
                          </div>
                          <div className="flex-1 min-w-0">
                            <h5 className="font-semibold text-gray-900 dark:text-white text-sm truncate group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                              {subcategory.title}
                            </h5>
                            <p className="text-xs text-gray-500 dark:text-gray-400 truncate mt-0.5">
                              {subcategory.description || 'No description available'}
                            </p>
                          </div>
                          <Icon icon="heroicons:chevron-right" className="w-4 h-4 text-gray-400 group-hover:text-blue-500 transition-colors" />
                        </Link>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <div className="mx-auto flex items-center justify-center w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full mb-4">
                        <Icon icon="heroicons:folder-open" className="w-8 h-8 text-gray-400" />
                      </div>
                      <h5 className="font-medium text-gray-900 dark:text-white mb-2">No subcategories yet</h5>
                      <p className="text-sm text-gray-500 dark:text-gray-400 mb-6 max-w-sm mx-auto">
                        Get started by creating your first subcategory or upload multiple subcategories at once.
                      </p>
                      <div className="flex flex-col sm:flex-row gap-3 justify-center">
                        <button
                          onClick={() => {
                            navigate(`/category/${selectedCategory.id}#create-subcategory`);
                          }}
                          className="inline-flex items-center justify-center gap-2 px-4 py-2.5 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
                        >
                          <Icon icon="heroicons:plus" className="w-4 h-4" />
                          Create First Subcategory
                        </button>
                        <Link
                          to={`/category/${selectedCategory.id}/bulk-upload`}
                          className="inline-flex items-center justify-center gap-2 px-4 py-2.5 text-sm font-medium text-blue-700 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 border border-blue-200 dark:border-blue-700 rounded-lg transition-all duration-200"
                        >
                          <Icon icon="heroicons:cloud-arrow-up" className="w-4 h-4" />
                          Bulk Upload
                        </Link>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="p-6 text-center">
                <Icon icon="heroicons:cursor-arrow-rays" className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h4 className="font-medium text-gray-900 dark:text-white mb-2 text-lg">
                  Select a Category
                </h4>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Click on a category from the list to view its subcategories and details.
                </p>
              </div>
            )}
          </Card>
        </div>
      </div>

      {/* Delete Modal */}
      {showDeleteModal && (
        <Delete
          showDeleteModal={showDeleteModal}
          setShowDeleteModal={setShowDeleteModal}
          data={deleteData}
        />
      )}
    </div>
  );
};

export default CategoryList;
