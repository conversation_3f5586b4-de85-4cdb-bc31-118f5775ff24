import React, { useState, useEffect, useRef } from "react";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import { useParams, useNavigate, Link } from "react-router-dom";
import Button from "@/components/ui/Button";
import CreateSubCategoryPage from "./SubCategory/CreateOrUpdate";
import Delete from "./SubCategory/Delete";
import Icon from "@/components/ui/Icon";
import Card from "@/components/ui/Card";
import Badge from "@/components/ui/Badge";
import SeoModal from "./SeoModal";

const Details = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  const [selectedSubCategoryId, setSelectedSubCategoryId] = useState(null);
  const [isCreatingSubCategory, setIsCreatingSubCategory] = useState(false);
  const [openDropdownId, setOpenDropdownId] = useState(null);
  const dropdownRefs = useRef({});

  const [deleteData, setDeleteData] = useState(null);
  const [deleteModal, setDeleteModal] = useState(false);
  const [seoModalOpen, setSeoModalOpen] = useState(false);
  const [imageZoomOpen, setImageZoomOpen] = useState(false);

  const { data: categoryData, isLoading: isCategoryLoading } = useGetApiQuery(
    `admin/categories/${id}?translations=1&request=1&sub_categories=1`
  );

  const handleDropdownToggle = (id, e) => {
    e.stopPropagation();
    setOpenDropdownId(openDropdownId === id ? null : id);
  };

  const handleDeleteSubCategory = (subCategory) => {
    setDeleteData(subCategory);
    setDeleteModal(true);
    setOpenDropdownId(null);
  };

  useEffect(() => {
    const handleClickOutside = (e) => {
      const isOutside = Object.values(dropdownRefs.current).every(ref => {
        return ref && !ref.contains(e.target);
      });

      if (isOutside) {
        setOpenDropdownId(null);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);

  const handleSubCategoryClick = (id) => {
    setSelectedSubCategoryId(id);
    setIsCreatingSubCategory(true);
  };

  const handleEdit = () => {
    navigate(`/categories/edit/${id}`);
  };

  if (isCategoryLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!categoryData?.data) {
    return (
      <div className="bg-white rounded-lg shadow p-6 text-center">
        <h3 className="text-lg font-medium text-gray-700">Category not found</h3>
        <Button
          icon="heroicons:chevron-left"
          onClick={() => navigate('/categories')}
          variant="outline"
          className="mt-4"
        >
          Back to Categories
        </Button>
      </div>
    );
  }

  const category = categoryData.data;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-4">
          <Button
            icon="heroicons:chevron-left"
            onClick={() => navigate('/categories')}
            variant="outline"
            className="h-10 w-10 p-0"
          />
          <div className="flex items-center gap-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                {category.title}
              </h1>
              <p className="text-gray-500 dark:text-gray-400">
                Category Details & Management
              </p>
            </div>
            {category.image && (
              <div
                className="relative h-16 w-16 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-600 cursor-pointer hover:shadow-lg transition-shadow duration-200"
                onClick={() => setImageZoomOpen(true)}
                title="Click to zoom"
              >
                <img
                  src={import.meta.env.VITE_ASSET_HOST_URL + category.image}
                  alt={category.title}
                  className="w-full h-full object-cover hover:scale-105 transition-transform duration-200"
                />
              </div>
            )}
          </div>
        </div>

        <div className="flex gap-3">
          <Button
            icon="heroicons:cog-6-tooth"
            onClick={() => setSeoModalOpen(true)}
            variant="outline"
            className="border-green-500 text-green-600 hover:bg-green-50"
          >
            SEO
          </Button>
          <Button
            icon="heroicons:pencil-square"
            onClick={handleEdit}
            className="bg-blue-500 hover:bg-blue-600 text-white"
          >
            Edit Category
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Basic Information Card */}
        <Card title="Basic Information" className="lg:col-span-2">
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Title</label>
                <p className="text-lg font-semibold text-gray-900 dark:text-white">{category.title}</p>
              </div>

            </div>

            <div>
              <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Description</label>
              <p className="text-gray-900 dark:text-white whitespace-pre-line">
                {category.description || <span className="text-gray-400">No description available</span>}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* <div>
                <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Color</label>
                <div className="flex items-center gap-3">
                  <div
                    className="w-8 h-8 rounded-lg border border-gray-200 dark:border-gray-600 shadow-sm"
                    style={{ backgroundColor: category.color }}
                  />
                  <span className="text-sm font-mono text-gray-700 dark:text-gray-300">{category.color}</span>
                </div>
              </div> */}
              <div>
                <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Featured</label>
                <Badge
                  className={
                    category.is_featured
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                  }
                >
                  {category.is_featured ? 'Featured' : 'Regular'}
                </Badge>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Premium</label>
                <Badge
                  className={
                    category.is_premium
                      ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
                      : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                  }
                >
                  {category.is_premium ? 'Premium' : 'Standard'}
                </Badge>
              </div>
            </div>
          </div>
        </Card>

        {/* Stats Card */}
        <Card title="Statistics" className="h-fit">
          <div className="space-y-4">
            <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {category.sub_categories?.length || 0}
              </div>
              <div className="text-sm text-blue-600 dark:text-blue-400">Subcategories</div>
            </div>

            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-500 dark:text-gray-400">Created</span>
                <span className="text-gray-900 dark:text-white">
                  {new Date(category.created_at).toLocaleDateString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500 dark:text-gray-400">Updated</span>
                <span className="text-gray-900 dark:text-white">
                  {new Date(category.updated_at).toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Media Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Images Card */}
        <Card title="Media" className="h-fit">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Main Image</label>
              {category.image ? (
                <div className="relative h-48 bg-gray-50 dark:bg-gray-800 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700">
                  <img
                    src={import.meta.env.VITE_ASSET_HOST_URL + category.image}
                    alt={category.title}
                    className="w-full h-full object-cover"
                  />
                </div>
              ) : (
                <div className="h-48 bg-gray-50 dark:bg-gray-800 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 flex items-center justify-center">
                  <div className="text-center">
                    <Icon icon="heroicons:photo" className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-500 dark:text-gray-400">No image available</p>
                  </div>
                </div>
              )}
            </div>


          </div>
        </Card>

        {/* SEO Information Card - Moved to dedicated SEO modal */}
        {/* <Card title="SEO Information" className="h-fit">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">SEO Title</label>
              <p className="text-gray-900 dark:text-white">
                {category.seo_title || <span className="text-gray-400">Not specified</span>}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">SEO Description</label>
              <p className="text-gray-900 dark:text-white text-sm">
                {category.seo_description || <span className="text-gray-400">Not specified</span>}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">SEO Keywords</label>
              <p className="text-gray-900 dark:text-white text-sm">
                {category.seo_keywords || <span className="text-gray-400">Not specified</span>}
              </p>
            </div>
          </div>
        </Card> */}
      </div>

      {/* Translations Section */}
      {category.translations?.length > 0 && (
        <Card title="Translations">
          <div className="space-y-4">
            {category.translations.map((translation) => (
              <div key={translation.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-3">
                  <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                    {translation.language.toUpperCase()}
                  </Badge>
                  <span className="text-sm text-gray-500 dark:text-gray-400">Translation</span>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <label className="block text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Title</label>
                    <p className="text-gray-900 dark:text-white">
                      {translation.title || <span className="text-gray-400">Not specified</span>}
                    </p>
                  </div>
                  {/* <div>
                    <label className="block text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">SEO Title</label>
                    <p className="text-gray-900 dark:text-white">
                      {translation.seo_title || <span className="text-gray-400">Not specified</span>}
                    </p>
                  </div> */}
                  <div className="md:col-span-2">
                    <label className="block text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Description</label>
                    <p className="text-gray-900 dark:text-white">
                      {translation.description || <span className="text-gray-400">Not specified</span>}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {isCreatingSubCategory ? (
        <CreateSubCategoryPage
          setIsCreatingSubCategory={setIsCreatingSubCategory}
          subcategroyid={selectedSubCategoryId}
        />
      ) : (
        <Card>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Subcategories</h2>
              <p className="text-gray-500 dark:text-gray-400 text-sm">
                Manage subcategories for this category
              </p>
            </div>
            <div className="flex gap-3">
              <Button
                icon="heroicons:cloud-arrow-up"
                onClick={() => navigate(`/category/${id}/bulk-upload`)}
                variant="outline"
                className="border-blue-500 text-blue-600 hover:bg-blue-50"
              >
                Bulk Upload
              </Button>
              <Button
                icon="heroicons:plus"
                onClick={() => {
                  setSelectedSubCategoryId(null);
                  setIsCreatingSubCategory(true);
                }}
                className="bg-primary-500 hover:bg-primary-600 text-white"
              >
                Add Subcategory
              </Button>
            </div>
          </div>

          {category?.sub_categories?.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {category.sub_categories.map((subCategory) => (
                <div
                  key={subCategory.id}
                  className="group relative bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden hover:shadow-lg transition-all duration-200"
                >
                  {/* Image */}
                  <div className="relative h-32 bg-gray-50 dark:bg-gray-700 overflow-hidden">
                    {subCategory.image ? (
                      <img
                        src={import.meta.env.VITE_ASSET_HOST_URL + subCategory.image}
                        alt={subCategory.title}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-gray-400 dark:text-gray-500">
                        <Icon icon="heroicons:photo" className="w-8 h-8" />
                      </div>
                    )}

                    {/* Action Dropdown */}
                    <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <div className="relative">
                        <button
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleDropdownToggle(subCategory.id, e);
                          }}
                          className="p-1.5 bg-white dark:bg-gray-800 rounded-full shadow-lg hover:bg-gray-50 dark:hover:bg-gray-700"
                        >
                          <Icon icon="heroicons:ellipsis-vertical" className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                        </button>

                        {openDropdownId === subCategory.id && (
                          <div
                            className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-20"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div className="py-1">
                              <button
                                onClick={() => {
                                  handleSubCategoryClick(subCategory.id);
                                  setOpenDropdownId(null);
                                }}
                                className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                              >
                                <Icon icon="heroicons:pencil-square" className="w-4 h-4 mr-2" />
                                Edit
                              </button>
                              <button
                                onClick={() => {
                                  handleDeleteSubCategory(subCategory);
                                  setOpenDropdownId(null);
                                }}
                                className="flex items-center w-full px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700"
                              >
                                <Icon icon="heroicons:trash" className="w-4 h-4 mr-2" />
                                Delete
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Content */}
                  <Link to={`/subcategory/${subCategory.id}`} className="block p-3 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                    <h3 className="font-semibold text-gray-900 dark:text-white text-sm mb-1 line-clamp-1">
                      {subCategory.title}
                    </h3>

                    <p className="text-gray-600 dark:text-gray-400 text-xs line-clamp-2 mb-2">
                      {subCategory.description || 'No description available'}
                    </p>

                    {/* <div className="flex items-center gap-2">
                      <div
                        className="w-3 h-3 rounded-full border border-gray-200 dark:border-gray-600"
                        style={{ backgroundColor: subCategory.color }}
                      />
                      <span className="text-xs text-gray-500 dark:text-gray-400 font-mono">
                        {subCategory.color}
                      </span>
                    </div> */}
                  </Link>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-gray-100 dark:bg-gray-700 mb-4">
                <Icon icon="heroicons:folder-plus" className="h-8 w-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No subcategories yet</h3>
              <p className="text-gray-500 dark:text-gray-400 mb-6">
                Get started by creating your first subcategory or upload multiple subcategories at once.
              </p>
              <div className="flex gap-3 justify-center">
                <Button
                  icon="heroicons:cloud-arrow-up"
                  onClick={() => navigate(`/category/${id}/bulk-upload`)}
                  variant="outline"
                  className="border-blue-500 text-blue-600 hover:bg-blue-50"
                >
                  Bulk Upload
                </Button>
                <Button
                  icon="heroicons:plus"
                  onClick={() => {
                    setSelectedSubCategoryId(null);
                    setIsCreatingSubCategory(true);
                  }}
                  className="bg-primary-500 hover:bg-primary-600 text-white"
                >
                  Create Subcategory
                </Button>
              </div>
            </div>
          )}
        </Card>
      )}

      {deleteModal && (
        <Delete
          showDeleteModal={deleteModal}
          setShowDeleteModal={setDeleteModal}
          data={deleteData}
        />
      )}

      {seoModalOpen && (
        <SeoModal
          isOpen={seoModalOpen}
          onClose={() => setSeoModalOpen(false)}
          data={category}
          type="category"
          onSuccess={() => {
            // Optionally refetch data or update local state
            window.location.reload(); // Simple refresh for now
          }}
        />
      )}

      {/* Image Zoom Modal */}
      {imageZoomOpen && category.image && (
        <div
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-[99999] p-4"
          onClick={() => setImageZoomOpen(false)}
        >
          <div className="relative max-w-4xl max-h-full">
            <button
              onClick={() => setImageZoomOpen(false)}
              className="absolute top-4 right-4 text-white hover:text-gray-300 z-10 bg-black bg-opacity-50 rounded-full p-2"
              title="Close"
            >
              <Icon icon="heroicons:x-mark" className="w-6 h-6" />
            </button>
            <img
              src={import.meta.env.VITE_ASSET_HOST_URL + category.image}
              alt={category.title}
              className="max-w-full max-h-full object-contain rounded-lg"
              onClick={(e) => e.stopPropagation()}
            />
            <div className="absolute bottom-4 left-4 right-4 text-center">
              <p className="text-white bg-black bg-opacity-50 rounded px-3 py-1 inline-block">
                {category.title}
              </p>
            </div>
          </div>
        </div>
      )}


    </div>
  );
};

export default Details;