import React, { useState, useRef } from 'react';
import { Formik, Form } from 'formik';
import { usePostApiMutation, useUpdateApiMutation, useGetApiQuery } from '@/store/api/master/commonSlice';
import Button from '@/components/ui/Button';
import { useNavigate, useParams } from 'react-router-dom';
import { skipToken } from "@reduxjs/toolkit/query";
import InputField from "@/components/ui/InputField";
import TextareaField from "@/components/ui/TextareaField";
import Card from "@/components/ui/Card";
import { toast } from 'react-toastify';
import Icon from "@/components/ui/Icon";
import { getInitialValues, validationSchema, USER_TYPE_OPTIONS, SUPPORT_TYPE_OPTIONS, PRIORITY_OPTIONS } from "./FormSettings";

const CreateOrUpdateSubscription = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const formikRef = useRef();
  const [submitAttempted, setSubmitAttempted] = useState(false);
  const [formSubmitting, setFormSubmitting] = useState(false);

  const { data: subscription, isLoading: isGetLoading } = useGetApiQuery(
    id ? `/admin/subscriptions/${id}` : skipToken
  );

  const [postApi, { isLoading: isPostLoading }] = usePostApiMutation();
  const [updateApi, { isLoading: isUpdateLoading }] = useUpdateApiMutation();

  const handleSubmit = async (values, { setErrors, resetForm }) => {
    setFormSubmitting(true);

    try {
      // Prepare the payload
      const payload = {
        name: values.name,
        description: values.description || null,
        price: parseFloat(values.price),
        duration_days: parseInt(values.duration_days),
        max_requests: values.max_requests ? parseInt(values.max_requests) : null,
        max_offers: values.max_offers ? parseInt(values.max_offers) : null,
        max_orders: values.max_orders ? parseInt(values.max_orders) : null,
        is_active: values.is_active,
        is_featured: values.is_featured,
        user_type: values.user_type,
        features: values.features
      };

      const response = id
        ? await updateApi({ end_point: '/admin/subscriptions/' + id, body: payload })
        : await postApi({ end_point: '/admin/subscriptions', body: payload });

      if (response.error) {
        setErrors(response.error.data.errors || { general: 'An error occurred' });
        toast.error(response.error.data.message || 'Failed to save subscription plan');
      } else {
        navigate('/subscriptions');
      }
    } catch (error) {
      console.error('Submit error:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setFormSubmitting(false);
    }
  };

  if (isGetLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          icon="heroicons:chevron-left"
          variant="outline"
          onClick={() => navigate('/subscriptions')}
          className="h-10 w-10 p-0"
        />
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            {id ? 'Edit Subscription Plan' : 'Create Subscription Plan'}
          </h1>
          <p className="text-gray-500 dark:text-gray-400">
            {id ? 'Update subscription plan details' : 'Create a new subscription plan'}
          </p>
        </div>
      </div>

      {/* Form */}
      <Card className="max-w-4xl">
        <div className="p-6">
          <Formik
            innerRef={formikRef}
            initialValues={getInitialValues(subscription?.data)}
            enableReinitialize
            validationSchema={validationSchema}
            onSubmit={handleSubmit}
            validateOnBlur
            validateOnChange
            validateOnMount
          >
            {({ values, setFieldValue, handleBlur, touched, errors, setTouched, setErrors, setFieldError, validateForm }) => (
              <Form className="space-y-6">
                {/* Error Summary */}
                {submitAttempted && Object.keys(errors).length > 0 && (
                  <Card className="border-red-200 bg-red-50">
                    <div className="flex items-start">
                      <div className="flex-shrink-0 mt-0.5">
                        <Icon icon="heroicons:exclamation-circle" className="h-5 w-5 text-red-400" />
                      </div>
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-red-800">There were errors with your submission</h3>
                        <div className="mt-2 text-sm text-red-700">
                          <ul className="list-disc list-inside space-y-1">
                            {Object.entries(errors).map(([field, error]) => (
                              <li key={field}>
                                {field.charAt(0).toUpperCase() + field.slice(1)}: {error}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>
                  </Card>
                )}

                {/* Basic Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="md:col-span-2">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                      Basic Information
                    </h3>
                  </div>

                  <InputField
                    name="name"
                    label="Plan Name"
                    placeholder="Enter plan name"
                    required
                  />

                  <div className="grid grid-cols-2 gap-4">
                    <InputField
                      name="price"
                      label="Price (USD)"
                      type="number"
                      step="0.01"
                      min="0"
                      placeholder="0.00"
                      required
                    />

                    <InputField
                      name="duration_days"
                      label="Duration (Days)"
                      type="number"
                      min="1"
                      placeholder="30"
                      required
                    />
                  </div>

                  <div className="md:col-span-2">
                    <TextareaField
                      name="description"
                      label="Description"
                      placeholder="Enter plan description (optional)"
                      rows={3}
                    />
                  </div>
                </div>

                {/* Plan Configuration */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="md:col-span-2">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                      Plan Configuration
                    </h3>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      User Type
                    </label>
                    <select
                      name="user_type"
                      value={values.user_type}
                      onChange={(e) => setFieldValue('user_type', e.target.value)}
                      className="form-control"
                    >
                      {USER_TYPE_OPTIONS.map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="is_active"
                        name="is_active"
                        checked={values.is_active}
                        onChange={(e) => setFieldValue('is_active', e.target.checked)}
                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      />
                      <label htmlFor="is_active" className="ml-2 block text-sm text-gray-900 dark:text-white">
                        Active Plan
                      </label>
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="is_featured"
                        name="is_featured"
                        checked={values.is_featured}
                        onChange={(e) => setFieldValue('is_featured', e.target.checked)}
                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      />
                      <label htmlFor="is_featured" className="ml-2 block text-sm text-gray-900 dark:text-white">
                        Featured Plan
                      </label>
                    </div>
                  </div>
                </div>

                {/* Plan Limits */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="md:col-span-3">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                      Plan Limits <span className="text-sm font-normal text-gray-500">(Leave empty for unlimited)</span>
                    </h3>
                  </div>

                  <InputField
                    name="max_requests"
                    label="Max Requests"
                    type="number"
                    min="1"
                    placeholder="Unlimited"
                  />

                  <InputField
                    name="max_offers"
                    label="Max Offers"
                    type="number"
                    min="1"
                    placeholder="Unlimited"
                  />

                  <InputField
                    name="max_orders"
                    label="Max Orders"
                    type="number"
                    min="1"
                    placeholder="Unlimited"
                  />
                </div>

                {/* Features */}
                <div className="space-y-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                    Plan Features
                  </h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Support Type
                      </label>
                      <select
                        name="features.support"
                        value={values.features.support}
                        onChange={(e) => setFieldValue('features.support', e.target.value)}
                        className="form-control"
                      >
                        {SUPPORT_TYPE_OPTIONS.map(option => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Priority Level
                      </label>
                      <select
                        name="features.priority"
                        value={values.features.priority}
                        onChange={(e) => setFieldValue('features.priority', e.target.value)}
                        className="form-control"
                      >
                        {PRIORITY_OPTIONS.map(option => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="analytics"
                          name="features.analytics"
                          checked={values.features.analytics}
                          onChange={(e) => setFieldValue('features.analytics', e.target.checked)}
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        />
                        <label htmlFor="analytics" className="ml-2 block text-sm text-gray-900 dark:text-white">
                          Analytics Access
                        </label>
                      </div>

                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="api_access"
                          name="features.api_access"
                          checked={values.features.api_access}
                          onChange={(e) => setFieldValue('features.api_access', e.target.checked)}
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        />
                        <label htmlFor="api_access" className="ml-2 block text-sm text-gray-900 dark:text-white">
                          API Access
                        </label>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="custom_branding"
                          name="features.custom_branding"
                          checked={values.features.custom_branding}
                          onChange={(e) => setFieldValue('features.custom_branding', e.target.checked)}
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        />
                        <label htmlFor="custom_branding" className="ml-2 block text-sm text-gray-900 dark:text-white">
                          Custom Branding
                        </label>
                      </div>

                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="dedicated_manager"
                          name="features.dedicated_manager"
                          checked={values.features.dedicated_manager}
                          onChange={(e) => setFieldValue('features.dedicated_manager', e.target.checked)}
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        />
                        <label htmlFor="dedicated_manager" className="ml-2 block text-sm text-gray-900 dark:text-white">
                          Dedicated Manager
                        </label>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Submit Buttons */}
                <div className="pt-4 flex justify-end gap-3 border-t border-gray-200">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => navigate('/subscriptions')}
                    className="h-10 px-6"
                  >
                    Cancel
                  </Button>
                  <Button
                    type="button"
                    isLoading={formSubmitting || isPostLoading || isUpdateLoading}
                    variant="primary"
                    className="h-10 px-6"
                    onClick={async () => {
                      if (formSubmitting) return;
                      setSubmitAttempted(true);

                      const touchedFields = {};
                      Object.keys(values).forEach(key => {
                        touchedFields[key] = true;
                      });
                      setTouched(touchedFields);

                      const errors = await validateForm();
                      if (Object.keys(errors).length === 0) {
                        formikRef.current.submitForm();
                      }
                    }}
                  >
                    {id ? 'Update Plan' : 'Create Plan'}
                  </Button>
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </Card>
    </div>
  );
};

export default CreateOrUpdateSubscription;
