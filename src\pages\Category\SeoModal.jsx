import React, { useState, useEffect } from 'react';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import Modal from '@/components/ui/Modal';
import Button from '@/components/ui/Button';
import InputField from '@/components/ui/InputField';
import Textarea from '@/components/ui/Textarea';
import KeywordsInput from './KeywordsInput';
import { useUpdateApiMutation } from '@/store/api/master/commonSlice';
import { toast } from 'react-toastify';

const SeoModal = ({
  isOpen,
  onClose,
  data,
  type = 'category', // 'category' or 'subcategory'
  onSuccess
}) => {
  const [updateApi, { isLoading: isUpdating }] = useUpdateApiMutation();
  const [submitAttempted, setSubmitAttempted] = useState(false);

  const validationSchema = Yup.object().shape({
    seo_title: Yup.string()
      .max(60, 'SEO title should be under 60 characters')
      .nullable(),
    seo_description: Yup.string()
      .max(160, 'SEO description should be under 160 characters')
      .nullable(),
    seo_keywords: Yup.string().nullable(),
  });

  const initialValues = {
    seo_title: data?.seo_title || '',
    seo_description: data?.seo_description || '',
    seo_keywords: data?.seo_keywords || '',
  };

  const handleSubmit = async (values) => {
    try {
      const endpoint = type === 'category'
        ? `admin/categories/${data.id}`
        : `admin/subcategories/${data.id}`;

      const formData = new FormData();
      formData.append('seo_title', values.seo_title || '');
      formData.append('seo_description', values.seo_description || '');
      formData.append('seo_keywords', values.seo_keywords || '');

      const response = await updateApi({
        url: endpoint,
        data: formData,
      }).unwrap();

      toast.success(`${type === 'category' ? 'Category' : 'Subcategory'} SEO updated successfully!`);
      onSuccess?.(response);
      onClose();
    } catch (error) {
      console.error('Error updating SEO:', error);
      toast.error(error?.data?.message || 'Failed to update SEO settings');
    }
  };

  const resetForm = () => {
    setSubmitAttempted(false);
  };

  useEffect(() => {
    if (!isOpen) {
      resetForm();
    }
  }, [isOpen]);

  return (
    <Modal
      activeModal={isOpen}
      onClose={onClose}
      title={`SEO Settings - ${data?.title || 'Item'}`}
      className="max-w-2xl"
    >
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {({ values, setFieldValue, handleBlur, errors, touched, validateForm }) => (
          <Form className="space-y-6">
            <div className="space-y-4">
              <div>
                <InputField
                  name="seo_title"
                  label="SEO Title"
                  type="text"
                  placeholder="Keep under 60 characters for optimal display"
                  onBlur={handleBlur}
                  error={submitAttempted && errors.seo_title}
                  maxLength={60}
                />
                <div className="text-xs text-gray-500 mt-1">
                  {values.seo_title?.length || 0}/60 characters
                </div>
              </div>

              <div>
                <Textarea
                  name="seo_description"
                  label="SEO Description"
                  rows={3}
                  placeholder="Keep under 160 characters for optimal display in search results"
                  value={values.seo_description}
                  onChange={(e) => setFieldValue('seo_description', e.target.value)}
                  onBlur={handleBlur}
                  error={submitAttempted && errors.seo_description}
                  maxLength={160}
                />
                <div className="text-xs text-gray-500 mt-1">
                  {values.seo_description?.length || 0}/160 characters
                </div>
              </div>

              <div>
                <KeywordsInput
                  name="seo_keywords"
                  label="SEO Keywords"
                  placeholder="Enter keywords separated by commas"
                />
                <div className="text-xs text-gray-500 mt-1">
                  Separate keywords with commas for better SEO targeting
                </div>
              </div>
            </div>

            <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isUpdating}
              >
                Cancel
              </Button>
              <Button
                type="button"
                variant="primary"
                isLoading={isUpdating}
                onClick={async () => {
                  setSubmitAttempted(true);
                  const errors = await validateForm();
                  if (Object.keys(errors).length === 0) {
                    handleSubmit(values);
                  }
                }}
              >
                Update SEO
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default SeoModal;
