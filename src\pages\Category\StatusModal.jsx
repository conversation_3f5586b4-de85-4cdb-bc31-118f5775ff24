import React from "react";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import Icon from "@/components/ui/Icon";
import Badge from "@/components/ui/Badge";
import { useUpdateApiMutation } from "@/store/api/master/commonSlice";
import { toast } from "react-toastify";

const StatusModal = ({ 
  showModal, 
  setShowModal, 
  category, 
  onSuccess 
}) => {
  const [updateApi, { isLoading }] = useUpdateApiMutation();

  const handleStatusChange = async () => {
    try {
      const formData = new FormData();
      formData.append('is_active', category.is_active ? 0 : 1);
      
      const response = await updateApi({
        end_point: `/admin/categories/${category.id}`,
        body: formData
      });

      if (response.error) {
        toast.error(response.error.data?.message || 'Failed to update category status');
      } else {
        toast.success(`Category ${category.is_active ? 'deactivated' : 'activated'} successfully`);
        setShowModal(false);
        if (onSuccess) {
          onSuccess();
        }
      }
    } catch (error) {
      console.error('Error updating category status:', error);
      toast.error('An error occurred while updating the category status');
    }
  };

  const actionText = category?.is_active ? 'Deactivate' : 'Activate';
  const actionColor = category?.is_active ? 'danger' : 'success';
  const iconName = category?.is_active ? 'heroicons:x-circle' : 'heroicons:check-circle';

  return (
    <Modal
      activeModal={showModal}
      onClose={() => setShowModal(false)}
      title={`${actionText} Category`}
      className="max-w-md"
      footer={
        <div className="flex items-center justify-end space-x-3">
          <Button
            text="Cancel"
            className="btn-outline-secondary"
            onClick={() => setShowModal(false)}
            disabled={isLoading}
          />
          <Button
            text={isLoading ? 'Processing...' : `${actionText} Category`}
            className={`btn-${actionColor}`}
            onClick={handleStatusChange}
            isLoading={isLoading}
            disabled={isLoading}
          />
        </div>
      }
    >
      <div className="py-4">
        {/* Category Info */}
        <div className="flex items-center gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg mb-6">
          <div className="relative h-12 w-12 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-600 flex-shrink-0">
            {category?.image ? (
              <img
                src={import.meta.env.VITE_ASSET_HOST_URL + category.image}
                alt={category.title}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                <Icon icon="heroicons:photo" className="w-6 h-6 text-gray-400" />
              </div>
            )}
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="font-medium text-gray-900 dark:text-white truncate">
              {category?.title}
            </h3>
            <div className="flex items-center gap-2 mt-1">
              <Badge
                className={
                  category?.is_active
                    ? 'bg-green-100 text-green-800 text-xs'
                    : 'bg-red-100 text-red-800 text-xs'
                }
              >
                {category?.is_active ? 'Active' : 'Inactive'}
              </Badge>
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {category?.sub_categories?.length || 0} subcategories
              </span>
            </div>
          </div>
        </div>

        {/* Confirmation Message */}
        <div className="text-center">
          <div className={`mx-auto flex items-center justify-center w-12 h-12 rounded-full mb-4 ${
            category?.is_active 
              ? 'bg-red-100 dark:bg-red-900/20' 
              : 'bg-green-100 dark:bg-green-900/20'
          }`}>
            <Icon 
              icon={iconName} 
              className={`w-6 h-6 ${
                category?.is_active 
                  ? 'text-red-600 dark:text-red-400' 
                  : 'text-green-600 dark:text-green-400'
              }`} 
            />
          </div>
          
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {actionText} Category
          </h3>
          
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
            {category?.is_active 
              ? 'Are you sure you want to deactivate this category? It will no longer be visible to users.'
              : 'Are you sure you want to activate this category? It will become visible to users.'
            }
          </p>

          {/* Warning for deactivation */}
          {category?.is_active && (
            <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-3 mb-4">
              <div className="flex items-start">
                <Icon icon="heroicons:exclamation-triangle" className="w-5 h-5 text-yellow-600 dark:text-yellow-400 mt-0.5 mr-2 flex-shrink-0" />
                <div className="text-left">
                  <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                    Impact of deactivation:
                  </p>
                  <ul className="text-xs text-yellow-700 dark:text-yellow-300 mt-1 space-y-1">
                    <li>• Category will be hidden from users</li>
                    <li>• All subcategories will also be affected</li>
                    <li>• Existing requests may be impacted</li>
                  </ul>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default StatusModal;
