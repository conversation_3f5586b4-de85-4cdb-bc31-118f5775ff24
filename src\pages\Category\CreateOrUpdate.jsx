import React, { useState, useEffect, useRef } from 'react';
import { Formik, Form, Field } from 'formik';
import * as Yup from 'yup';
// import KeywordsInput from './KeywordsInput';
import { usePostApiMutation, useUpdateApiMutation, useGetApiQuery } from '@/store/api/master/commonSlice';
import Button from '@/components/ui/Button';
import { useNavigate, useParams } from 'react-router-dom';
import { skipToken } from "@reduxjs/toolkit/query";
import InputField from "@/components/ui/InputField";
import Fileinput from "@/components/ui/Fileinput";
import Textarea from "@/components/ui/Textarea";
import { FiChevronLeft, FiSave, FiX, FiAlertCircle } from 'react-icons/fi';
import Card from '@/components/ui/Card';
import SectionHeader from '@/components/ui/SectionHeader';
import Badge from '@/components/ui/Badge';

const CreateCategoryPage = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const formikRef = useRef();

  const { data: categoryData } = useGetApiQuery(
    id ? `admin/categories/${id}` : skipToken
  );

  const [postApi, { isLoading: isPostLoading }] = usePostApiMutation();
  const [updateApi, { isLoading: isUpdateLoading }] = useUpdateApiMutation();

  const [imagePreview, setImagePreview] = useState(null);
  const [submitAttempted, setSubmitAttempted] = useState(false);
  const [formSubmitting, setFormSubmitting] = useState(false);

  const validationSchema = Yup.object().shape({
    title: Yup.string().required('Title is required'),
    description: Yup.string(),
    // color: Yup.string()
    //   .matches(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'Invalid hex color')
    //   .required('Color is required'),
    image: id
      ? Yup.mixed()
      : Yup.mixed()
          .required('Image is required')
          .test('is-file', 'Image is required', value => {
            return value instanceof File || value === null || value === undefined;
          }),
    is_featured: Yup.boolean(),
    is_premium: Yup.boolean(),
    // seo_title: Yup.string(),
    // seo_description: Yup.string(),
    // seo_keywords: Yup.string(),
    translations: Yup.array().of(
      Yup.object().shape({
        language: Yup.string().required(),
        title: Yup.string().required('Translation title is required'),
        description: Yup.string(),
        // seo_title: Yup.string(),
        // seo_description: Yup.string(),
        // seo_keywords: Yup.string(),
      })
    ),
  });

  const getInitialValues = () => ({
    title: '',
    description: '',
    // color: '#3b82f6', // Changed default to a nice blue
    image: null,
    is_featured: false,
    is_premium: false,
    // seo_title: '',
    // seo_description: '',
    // seo_keywords: '',
    translations: [],
  });

  useEffect(() => {
    if (categoryData?.data && formikRef.current) {
      const category = categoryData.data;
      formikRef.current.setFieldValue('title', category.title || '');
      formikRef.current.setFieldValue('description', category.description || '');
      // formikRef.current.setFieldValue('color', category.color || '#3b82f6');
      formikRef.current.setFieldValue('image', null);
      formikRef.current.setFieldValue('is_featured', category.is_featured || false);
      formikRef.current.setFieldValue('is_premium', category.is_premium || false);
      // formikRef.current.setFieldValue('seo_title', category.seo_title || '');
      // formikRef.current.setFieldValue('seo_description', category.seo_description || '');
      // formikRef.current.setFieldValue('seo_keywords', category.seo_keywords || '');
      formikRef.current.setFieldValue('translations', category.translations || []);

      if (category.image) {
        const imageUrl = import.meta.env.VITE_ASSET_HOST_URL + category.image;
        setImagePreview(imageUrl);
      }
    }
  }, [categoryData]);

  const handleSubmit = async (values, { setErrors, resetForm }) => {
    try {
      setFormSubmitting(true);
      const formData = new FormData();
      formData.append('title', values.title);
      formData.append('description', values.description);
      // formData.append('color', values.color);

      if (values.image instanceof File) {
        formData.append('image', values.image);
      }

      formData.append('is_featured', values.is_featured ? 1 : 0);
      formData.append('is_premium', values.is_premium ? 1 : 0);
      // formData.append('seo_title', values.seo_title);
      // formData.append('seo_description', values.seo_description);
      // formData.append('seo_keywords', values.seo_keywords);

      if (values.translations.length > 0) {
        formData.append('translations', JSON.stringify(values.translations));
      }

      const response = id
        ? await updateApi({ end_point: '/admin/categories/' + id, body: formData })
        : await postApi({ end_point: '/admin/categories', body: formData });

      if (response.error) {
        setErrors(response.error.data.errors || { general: 'An error occurred' });
      } else {
        resetForm();
        navigate('/categories');
      }
    } catch (error) {
      console.error('Error:', error);
    } finally {
      setFormSubmitting(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 bg-gray-50 flex items-center justify-between">
        <div className="flex items-center">
          <button
            onClick={() => navigate('/categories')}
            className="mr-3 p-1 rounded-md hover:bg-gray-100 text-gray-600 hover:text-gray-900 transition-colors"
          >
            <FiChevronLeft className="w-5 h-5" />
          </button>
          <div>
            <h2 className="text-lg font-semibold text-gray-800">
              {id ? 'Edit Category' : 'Create New Category'}
            </h2>
            <p className="text-xs text-gray-500 mt-1">
              {id ? 'Update existing category details' : 'Add a new category to your catalog'}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            type="button"
            variant="outline"
            onClick={() => navigate('/categories')}
            className="h-9 px-4 flex items-center"
          >
            <FiX className="mr-2 w-4 h-4" /> Cancel
          </Button>
          <Button
            type="button"
            isLoading={formSubmitting || isPostLoading || isUpdateLoading}
            variant="primary"
            className="h-9 px-4 flex items-center"
            onClick={async () => {
              if (formSubmitting) return; // Prevent multiple submissions

              setSubmitAttempted(true);
              const touchedFields = {};
              Object.keys(getInitialValues()).forEach(key => {
                touchedFields[key] = true;
              });
              formikRef.current.setTouched(touchedFields);

              if (!id) {
                if (!formikRef.current.values.image) {
                  formikRef.current.setFieldError('image', 'Image is required');
                }
              }

              const errors = await formikRef.current.validateForm();
              if (Object.keys(errors).length === 0) {
                formikRef.current.handleSubmit();
              }
            }}
          >
            <FiSave className="mr-2 w-4 h-4" /> {id ? 'Update' : 'Save'} Category
          </Button>
        </div>
      </div>

      {/* Form Content */}
      <div className="p-6">
        <Formik
          innerRef={formikRef}
          initialValues={getInitialValues()}
          enableReinitialize
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
          validateOnBlur
          validateOnChange
          validateOnMount
        >
          {({ values, setFieldValue, handleBlur, touched, errors, setTouched, setErrors, setFieldError }) => (
            <Form className="space-y-6">
              {/* Error Summary */}
              {submitAttempted && Object.keys(errors).length > 0 && (
                <Card className="border-red-200 bg-red-50">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 mt-0.5">
                      <FiAlertCircle className="h-5 w-5 text-red-400" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-red-800">There were errors with your submission</h3>
                      <div className="mt-2 text-sm text-red-700">
                        <ul className="list-disc pl-5 space-y-1">
                          {Object.entries(errors).map(([field, error]) => (
                            typeof error === 'string' ? (
                              <li key={field}>{error}</li>
                            ) : null
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                </Card>
              )}

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Left Column */}
                <div className="space-y-6">
                  {/* Basic Information Card */}
                  <Card>
                    <SectionHeader
                      title="Basic Information"
                      description="Core details about the category"
                    />
                    <div className="space-y-4">
                      <InputField
                        name="title"
                        label="Title"
                        type="text"
                        required
                        placeholder="Category title"
                        onBlur={handleBlur}
                        error={submitAttempted && errors.title}
                        className="h-10"
                      />

                      <Textarea
                        name="description"
                        label="Description"
                        rows={3}
                        placeholder="Category description"
                        value={values.description}
                        onChange={(e) => setFieldValue('description', e.target.value)}
                        onBlur={handleBlur}
                        error={submitAttempted && errors.description}
                      />

                      <div className="flex items-center space-x-6">
                        <div className="flex items-center">
                          <Field
                            name="is_featured"
                            type="checkbox"
                            id="is_featured"
                            className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                          />
                          <label htmlFor="is_featured" className="ml-2 block text-sm text-gray-700">Featured</label>
                        </div>
                        <div className="flex items-center">
                          <Field
                            name="is_premium"
                            type="checkbox"
                            id="is_premium"
                            className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                          />
                          <label htmlFor="is_premium" className="ml-2 block text-sm text-gray-700">Premium</label>
                        </div>
                      </div>

                      {/* <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Color</label>
                        <div className="flex items-center gap-3">
                          <Field
                            name="color"
                            type="color"
                            className="h-10 w-10 cursor-pointer rounded-md border border-gray-300 p-0"
                          />
                          <div className="flex-1">
                            <InputField
                              name="color"
                              type="text"
                              placeholder="#3b82f6"
                              onBlur={handleBlur}
                              error={submitAttempted && errors.color}
                              required
                              className="h-10"
                            />
                          </div>
                        </div>
                      </div> */}
                    </div>
                  </Card>

                  {/* SEO Settings Card - Moved to dedicated modal */}
                  {/* <Card>
                    <SectionHeader
                      title="SEO Settings"
                      description="Search engine optimization details"
                      badge={<Badge variant="info" size="xs">Recommended</Badge>}
                    />
                    <div className="space-y-4">
                      <InputField
                        name="seo_title"
                        label="SEO Title"
                        type="text"
                        placeholder="SEO title for search engines"
                        onBlur={handleBlur}
                        error={submitAttempted && errors.seo_title}
                        className="h-10"
                      />

                      <Textarea
                        name="seo_description"
                        label="SEO Description"
                        rows={2}
                        placeholder="SEO description for search engines"
                        value={values.seo_description}
                        onChange={(e) => setFieldValue('seo_description', e.target.value)}
                        onBlur={handleBlur}
                        error={submitAttempted && errors.seo_description}
                      />

                      <KeywordsInput name="seo_keywords" />
                    </div>
                  </Card> */}
                </div>

                {/* Right Column */}
                <div className="space-y-6">
                  {/* Media Card */}
                  <Card>
                    <SectionHeader
                      title="Media"
                      description="Images for the category"
                      badge={id ? null : <Badge variant="danger" size="xs">Required</Badge>}
                    />
                    <div className="space-y-4">
                      <Fileinput
                        title="Main Image"
                        name="image"
                        label="Select Image"
                        placeholder="Choose an image..."
                        accept="image/*"
                        preview
                        className="h-10"
                        onChange={(e) => {
                          const file = e.target.files[0];
                          if (file) {
                            setFieldValue('image', file);
                            setFieldError('image', undefined);
                            setErrors({ ...errors, image: undefined });
                          }
                        }}
                        fieldName="image"
                        previewImage={imagePreview}
                        setPreviewImage={setImagePreview}
                        selectedFile={values.image}
                        required={!id}
                        error={submitAttempted && errors.image}
                        onBlur={() => {
                          setTouched({ ...touched, image: true });
                          if (values.image instanceof File) {
                            setFieldError('image', undefined);
                            setErrors({ ...errors, image: undefined });
                          }
                          handleBlur({ target: { name: 'image' } });
                        }}
                      />
                    </div>
                  </Card>

                  {/* Translations Card */}
                  {values?.translations.length > 0 && (
                    <Card>
                      <SectionHeader
                        title="Translations"
                        description="Localized content for different languages"
                        badge={<Badge variant="primary" size="xs">{values.translations.length}</Badge>}
                      />
                      <div className="space-y-4">
                        {values.translations.map((_, index) => (
                          <div key={index} className="p-4 bg-gray-50 rounded-md border border-gray-200">
                            <h4 className="text-sm font-medium text-gray-700 mb-3">
                              {values.translations[index].language === 'bn' ? 'Bengali' : 'Arabic'} Translation
                            </h4>

                            <div className="space-y-4">
                              <InputField
                                name={`translations.${index}.title`}
                                label="Title"
                                type="text"
                                required
                                onBlur={handleBlur}
                                error={submitAttempted && errors.translations?.[index]?.title}
                                className="h-10"
                              />

                              <Textarea
                                name={`translations.${index}.description`}
                                label="Description"
                                rows={2}
                                value={values.translations[index]?.description || ''}
                                onChange={(e) => setFieldValue(`translations.${index}.description`, e.target.value)}
                                onBlur={handleBlur}
                                error={submitAttempted && errors.translations?.[index]?.description}
                              />

                              {/* <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <InputField
                                  name={`translations.${index}.seo_title`}
                                  label="SEO Title"
                                  type="text"
                                  onBlur={handleBlur}
                                  error={submitAttempted && errors.translations?.[index]?.seo_title}
                                  className="h-10"
                                />

                                <InputField
                                  name={`translations.${index}.seo_description`}
                                  label="SEO Description"
                                  type="text"
                                  onBlur={handleBlur}
                                  error={submitAttempted && errors.translations?.[index]?.seo_description}
                                  className="h-10"
                                />
                              </div>

                              <InputField
                                name={`translations.${index}.seo_keywords`}
                                label="SEO Keywords"
                                type="text"
                                onBlur={handleBlur}
                                error={submitAttempted && errors.translations?.[index]?.seo_keywords}
                                className="h-10"
                              /> */}
                            </div>
                          </div>
                        ))}
                      </div>
                    </Card>
                  )}
                </div>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default CreateCategoryPage;